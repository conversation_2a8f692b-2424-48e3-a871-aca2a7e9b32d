"use client";
import { FormEvent, useEffect, useState } from "react";
import { useParams, useRouter } from "next/navigation";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { ORDER_STATUS } from "@/app/data/fakeOrders";

const statusOptions = [
  ORDER_STATUS.COMPLETED,
  ORDER_STATUS.PENDING,
  ORDER_STATUS.CANCELLED,
];
const statusColors: Record<string, string> = {
  [ORDER_STATUS.COMPLETED]: "bg-green-100 text-green-800",
  [ORDER_STATUS.PENDING]: "bg-yellow-100 text-yellow-800",
  [ORDER_STATUS.CANCELLED]: "bg-red-100 text-red-800",
};

export type Order = {
  id: string;
  customer: string;
  status: string;
  amount: number;
  date: string;
  productName?: string;
  price?: number;
  quantity?: number;
  category?: string;
  description?: string;
  customerEmail?: string;
};

const OrderDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const orderId = params?.id ? params.id.toString() : "";
  const [form, setForm] = useState<Order | undefined>(undefined);
  const [saved, setSaved] = useState(false);

  const API = process.env.NEXT_PUBLIC_MY_API;

  useEffect(() => {
    if (!orderId) return;
    const fetchOrder = async () => {
      try {
        const res = await fetch(`${API}/api/order/${orderId}`);
        if (!res.ok) return setForm(undefined);
        const data = await res.json();
        setForm(data);
      } catch {
        setForm(undefined);
      }
    };
    fetchOrder();
  }, [orderId]);

  if (!form) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50">
        <div className="text-2xl font-bold text-gray-800 mb-2">
          Order Not Found
        </div>
      </div>
    );
  }

  const handleChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value } = e.target;
    setForm((prev) =>
      prev
        ? { ...prev, [name]: name === "amount" ? parseFloat(value) : value }
        : prev
    );
    setSaved(false);
  };

  const handleSave = async (e: FormEvent) => {
    e.preventDefault();
    if (!form) return;
    try {
      await fetch(`${API}/api/order/${orderId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(form),
      });
      setSaved(true);
      router.push(`/?highlight=${form.id}`);
    } catch {
      setSaved(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-white flex flex-col items-center py-10 px-4">
      <div className="w-full max-w-xl bg-white/90 rounded-2xl shadow-lg p-8 border border-gray-100">
        <Link
          href="/"
          className="text-blue-600 hover:underline flex items-center gap-1 mb-6"
        >
          <ArrowLeft className="w-4 h-4" /> Back to Dashboard
        </Link>
        <h1 className="text-3xl font-bold text-gray-900 mb-4 tracking-tight">
          Order Details
        </h1>
        <div className="mb-6 text-gray-500 text-sm">
          Order ID:{" "}
          <span className="font-semibold text-gray-800">{form.id}</span>
        </div>
        <form
          onSubmit={handleSave}
          className="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6"
        >
          <div>
            <label className="block text-xs font-medium text-gray-500 mb-1">
              Customer
            </label>
            <input
              name="customer"
              value={form.customer || ""}
              onChange={handleChange}
              className="font-semibold text-gray-800 w-full px-3 py-2 border border-gray-200 rounded-xl
                         bg-gray-50 focus:ring-2 focus:ring-blue-200 focus:outline-none transition"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-500 mb-1">
              Status
            </label>
            <select
              name="status"
              value={form.status || ""}
              onChange={handleChange}
              className={`w-full px-3 py-2 border border-gray-200 rounded-xl 
                        bg-gray-50 focus:ring-2 focus:ring-blue-200 focus:outline-none transition ${
                          form.status ? statusColors[form.status] : ""
                        }`}
            >
              {statusOptions.map((opt) => (
                <option key={opt} value={opt}>
                  {opt}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-500 mb-1">
              Total Amount
            </label>
            <input
              name="amount"
              type="number"
              min="0"
              step="0.01"
              value={form.amount || ""}
              onChange={handleChange}
              className="font-semibold text-gray-800 w-full px-3 py-2 border border-gray-200 rounded-xl
                       bg-gray-50 focus:ring-2 focus:ring-blue-200 focus:outline-none transition"
            />
          </div>
          <div>
            <label className="block text-xs font-medium text-gray-500 mb-1">
              Date
            </label>
            <input
              name="date"
              value={form.date || ""}
              onChange={handleChange}
              className="font-semibold text-gray-800 w-full px-3 py-2 border border-gray-200 rounded-xl
                       bg-gray-50 focus:ring-2 focus:ring-blue-200 focus:outline-none transition"
            />
          </div>
          {form.productName !== undefined && (
            <div className="sm:col-span-2">
              <label className="block text-xs font-medium text-gray-500 mb-1">
                Product
              </label>
              <input
                name="productName"
                value={form.productName}
                onChange={handleChange}
                className="font-semibold text-gray-800 w-full px-3 py-2 border border-gray-200 rounded-xl
                         bg-gray-50 focus:ring-2 focus:ring-blue-200 focus:outline-none transition"
              />
            </div>
          )}
          {form.category !== undefined && (
            <div className="sm:col-span-2">
              <label className="block text-xs font-medium text-gray-500 mb-1">
                Category
              </label>
              <input
                name="category"
                value={form.category}
                onChange={handleChange}
                className="font-semibold text-gray-800 w-full px-3 py-2 border border-gray-200 rounded-xl
                           bg-gray-50 focus:ring-2 focus:ring-blue-200 focus:outline-none transition"
              />
            </div>
          )}
          {form.customerEmail !== undefined && (
            <div className="sm:col-span-2">
              <label className="block text-xs font-medium text-gray-500 mb-1">
                Customer Email
              </label>
              <input
                name="customerEmail"
                value={form.customerEmail}
                onChange={handleChange}
                className="font-semibold text-gray-800 w-full px-3 py-2 border border-gray-200 rounded-xl
                        bg-gray-50 focus:ring-2 focus:ring-blue-200 focus:outline-none transition"
              />
            </div>
          )}
          {form.description !== undefined && (
            <div className="sm:col-span-2">
              <label className="block text-xs font-medium text-gray-500 mb-1">
                Description
              </label>
              <textarea
                name="description"
                value={form.description}
                onChange={handleChange}
                className="font-semibold text-gray-800 w-full px-3 py-2 border border-gray-200 rounded-xl
                         bg-gray-50 focus:ring-2 focus:ring-blue-200 focus:outline-none transition"
              />
            </div>
          )}
          <div className="sm:col-span-2 flex flex-col pt-2">
            <button
              type="submit"
              className="w-full px-5 py-1 bg-blue-600 text-white rounded-xl 
                       hover:bg-blue-700 transition-colors shadow-sm text-lg font-semibold"
            >
              Save
            </button>
            {saved && (
              <span className="text-green-600 text-sm self-center mt-2">
                Saved!
              </span>
            )}
          </div>
        </form>
        <div className="mb-8 p-4 bg-gray-50 rounded-xl border border-gray-200">
          <h2 className="text-xl font-semibold mb-2 text-gray-800">
            Product Details
          </h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <span className="block text-xs text-gray-500">Product Name</span>
              <span className="font-medium text-gray-900">
                {form.productName || "-"}
              </span>
            </div>
            <div>
              <span className="block text-xs text-gray-500">Category</span>
              <span className="font-medium text-gray-900">
                {form.category || "-"}
              </span>
            </div>
            <div>
              <span className="block text-xs text-gray-500">Price</span>
              <span className="font-medium text-gray-900">
                {form.price ? `$${form.price}` : "-"}
              </span>
            </div>
            <div>
              <span className="block text-xs text-gray-500">Quantity</span>
              <span className="font-medium text-gray-900">
                {form.quantity || "-"}
              </span>
            </div>
            <div className="sm:col-span-2">
              <span className="block text-xs text-gray-500">Description</span>
              <span className="font-medium text-gray-900">
                {form.description || "-"}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderDetailPage;
