"use client";

import React, { useActionState, useLayoutEffect, useEffect } from "react";
import toast, { Toaster } from "react-hot-toast";
import Link from "next/link";
import { login } from "./login_action";

export default function LoginPage() {

  const [state, action, loading] = useActionState(login, null);

  useLayoutEffect(() => {
    if (state === "ok") window.location.href = "/dashboard/";
  }, [state]);

  useEffect(() => {
    if (state && state !== "ok") {
      toast.error(state || "Login failed. Please check your credentials.");
    }
  }, [state]);
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Toaster position="top-center" />
      <div className="bg-white p-8 rounded-lg shadow-md w-full max-w-md border border-gray-200">
        <h2 className="text-2xl font-bold mb-6 text-center">Login</h2>
        <form action={action} className="space-y-4" >
          <div>
            <label className="block text-sm font-medium mb-1">E-Mail</label>
            <input
              type="email"
              name="email"
              placeholder="Enter your email"
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200"
              required
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">Password</label>
            <input
              type="password"
              name="password"
              placeholder="Enter your password"
              className="w-full border border-gray-300 rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-200"
              required
            />
          </div>
          <button
            disabled={loading}
            type="submit"
            className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition"
          >
            {loading ? "Logging in..." : "Login"}
          </button>
        </form>
        <div className="mt-4 text-center text-sm">
          Don't have an account?{" "}
          <Link href="/register" className="text-blue-600 hover:underline">
            Register
          </Link>
        </div>
      </div>
    </div>
  );
}
