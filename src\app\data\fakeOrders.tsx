type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS];
  
export const ORDER_STATUS = {
  COMPLETED: 'Completed',
  PENDING: 'Pending',
  CANCELLED: 'Cancelled',
} as const;

  
  type Order = {
  id: string;
  customer: string;
  status: OrderStatus;
  amount: number;
  date: string;
  productName?: string;
  price?: number;
  quantity?: number;
  category?: string;
  description?: string;
  customerEmail?: string;
};
  


    export type { OrderStatus , Order };