import { ArrowLeft } from "lucide-react";
import { deleteToken } from "../../cookieStore";


function LogoutButton() {
  const handleLogout = async () => {
    await fetch("/logout", { method: "GET" });
    await deleteToken();
    window.location.href = "/login";
  };
  return (
    <button
      onClick={handleLogout}
      className="group relative flex items-center justify-center w-10 h-10 bg-red-600 rounded-full transition-all duration-300 overflow-hidden hover:w-32 hover:justify-start"
      style={{ minWidth: 40, minHeight: 40 }}
    >
      <span className="flex items-center justify-center w-10 h-10">
        <ArrowLeft className="w-5 h-5 text-white" />
      </span>
      <span className="absolute left-12 opacity-0 group-hover:opacity-100 group-hover:left-12 transition-all duration-300 text-white font-semibold whitespace-nowrap">
        Logout
      </span>
    </button>
  );
}

export default LogoutButton;