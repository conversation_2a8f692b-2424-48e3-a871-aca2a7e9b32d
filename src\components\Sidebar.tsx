import React from 'react';
import { X, type LucideIcon } from 'lucide-react';
import type { SidebarKey } from '../app/(protected)/dashboard/page'; // Assuming SidebarKey is exported from page.tsx

export type SidebarItem = {
  icon: LucideIcon;
  label: string;
  key: Sidebar<PERSON>ey;
};

type SidebarProps = {
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
  activeContent: SidebarKey;
  onItemClick: (key: <PERSON>bar<PERSON>ey) => void;
  items: SidebarItem[];
};

import LogoutButton from "./LogoutButton";

export const Sidebar: React.FC<SidebarProps> = ({ isOpen, setIsOpen, activeContent, onItemClick, items }) => {
  return (
    <>
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/40 transition-opacity duration-300 z-40 lg:hidden"
          onClick={() => setIsOpen(false)}
        />
      )}
      <div className={`${isOpen ? 'translate-x-0' : '-translate-x-full'} lg:translate-x-0 fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white shadow-sm border-r border-gray-200 flex-shrink-0 transition-transform duration-500 ease-in-out flex flex-col`}>
        <div className="p-4 lg:p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                <div className="w-4 h-4 bg-white rounded-sm"></div>
              </div>
              <span className="text-lg lg:text-xl font-bold text-gray-900">FlowStock</span>
            </div>
            <button onClick={() => setIsOpen(false)} className="lg:hidden p-1 text-gray-500 hover:text-gray-700">
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>
        <nav className="mt-2 lg:mt-6 flex-1 overflow-y-auto px-3">
          {items.map((item) => (
            <a key={item.key} href="#" onClick={(e) => { e.preventDefault(); onItemClick(item.key);}} className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg mb-1 transition-colors ${activeContent === item.key ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700' : 'text-gray-700 hover:bg-gray-50'}`}>
              <item.icon className="w-5 h-5 mr-3 flex-shrink-0" />
              <span className="truncate">{item.label}</span>
            </a>
          ))}
        </nav>
        <div className="mt-auto mb-6 px-3">
          <LogoutButton />
        </div>
      </div>
    </>
  );
};