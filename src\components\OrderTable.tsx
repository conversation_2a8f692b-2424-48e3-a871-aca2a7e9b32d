import React from 'react';
import { <PERSON>, FileText, Trash2, MoreHorizontal } from 'lucide-react';
import type { Order, OrderStatus } from '../app/(protected)/dashboard/page'; // Assuming types are exported

type OrderTableProps = {
  orders: Order[];
  highlightedOrderId: string | null;
  getStatusColorClass: (status: OrderStatus) => string;
  onDeleteOrder: (orderId: string) => void;
  onEditOrder?: (order: Order) => void;
};

export const OrderTable: React.FC<OrderTableProps> = ({ orders, highlightedOrderId, getStatusColorClass, onDeleteOrder, onEditOrder }) => {
  if (orders.length === 0) {
    return (
      <div className="hidden lg:block overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr><td colSpan={6} className="px-6 py-4 text-center text-gray-500">No orders found.</td></tr>
          </tbody>
        </table>
      </div>
    );
  }

  return (
    <div className="hidden lg:block overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Order ID</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Amount</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {orders.map((order) => (
            <tr key={order.id} className={`hover:bg-gray-50 transition-all duration-500 ease-in-out ${order.id === highlightedOrderId ? 'bg-green-100' : ''}`}>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{order.id}</td>
              <td className="px-6 py-4 whitespace-nowrap">
                <div className="flex items-center">
                  <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center flex-shrink-0">
                    <span className="text-xs font-medium text-gray-600">{order.customer.split(' ').map(n => n[0]).join('')}</span>
                  </div>
                  <span className="ml-3 text-sm text-gray-900">{order.customer}</span>
                </div>
              </td>
              <td className="px-6 py-4 whitespace-nowrap"><span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColorClass(order.status)}`}>{order.status}</span></td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${order.amount.toFixed(2)}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{order.date}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex items-center space-x-2">
                  <button
                    className="p-1 hover:bg-gray-100 rounded"
                    title="View Details"
                    onClick={() => window.location.href = `/orders/${order.id.replace('#', '')}`}
                  >
                    <Eye className="w-4 h-4" />
                  </button>
                  <button className="p-1 hover:bg-gray-100 rounded" title="Send Invoice"><FileText className="w-4 h-4" /></button>
                  <button onClick={() => onDeleteOrder(order.id)} className="p-1 hover:bg-gray-100 rounded" title="Delete"><Trash2 className="w-4 h-4 text-red-500" /></button>
                  <button className="p-1 hover:bg-gray-100 rounded" title="Edit" onClick={() => onEditOrder && onEditOrder(order)}><MoreHorizontal className="w-4 h-4" /></button>
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};