"use server";

import { cookies } from "next/headers";

export async function getProducts() {
  try {
    const token = (await cookies()).get("token")?.value;
    // Add query parameter to filter only non-deleted products
    const res = await fetch("http://localhost:5000/api/product?is_deleted=false", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });

    if (!res.ok) throw new Error("Failed to fetch products");
    const products = await res.json();

    // Additional client-side filtering as backup
    const filteredProducts = Array.isArray(products)
      ? products.filter(product => product.is_deleted === false || product.is_deleted === null)
      : products;

    console.log("Fetched products (filtered):", filteredProducts.length);
    return filteredProducts;
  } catch (error) {
    console.error("Error fetching products:", error);
    return [];
  }
}

export async function updateProduct(id: string, updates: any) {
  const res = await fetch(`http://localhost:5000/api/product/${id}`, {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(updates),
  });
  if (!res.ok) throw new Error("Failed to update product");
  return res.json();
}

export async function deleteProduct(id: string) {
  const token = (await cookies()).get("token")?.value;
  
  if (token) {
    await fetch(`http://localhost:5000/api/product/softdelete/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });
  }
}
