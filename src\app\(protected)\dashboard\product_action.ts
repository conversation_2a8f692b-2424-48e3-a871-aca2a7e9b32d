"use server";

import { cookies } from "next/headers";

export async function getProducts() {
  const token = (await cookies()).get("token")?.value;
  const res = await fetch("http://localhost:5000/api/product", {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: "Bearer " + token,
    },
  }
);

  if (!res.ok) throw new Error("Failed to fetch products");
  return res.json();
}

export async function updateProduct(id: string, updates: any) {
  const res = await fetch(`http://localhost:5000/api/product/${id}`, {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(updates),
  });
  if (!res.ok) throw new Error("Failed to update product");
  return res.json();
}

export async function deleteProduct(id: string) {
  const token = (await cookies()).get("token")?.value;
  
  if (token) {
    await fetch(`http://localhost:5000/api/product/softdelete/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });
  }
}
