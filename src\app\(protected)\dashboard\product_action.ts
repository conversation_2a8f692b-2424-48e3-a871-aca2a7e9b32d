"use server";

import { cookies } from "next/headers";

export async function getProducts() {
  console.log("🔍 getProducts function called");
  try {
    const token = (await cookies()).get("token")?.value;
    console.log("🔑 Token found:", token ? "Yes" : "No");
        
    const res = await fetch("http://localhost:5000/api/product", {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });

    console.log("📡 Response status:", res.status);
    console.log("✅ Response ok:", res.ok);

    if (!res.ok) {
      console.log("❌ Response not ok:", res.status);
      throw new Error(`Failed to fetch products: ${res.status}`);
    }

    const data = await res.json();
    console.log("📦 Raw products from backend:", data);
    console.log("📊 Total products received:", Array.isArray(data) ? data.length : 0);

    const filteredData = Array.isArray(data)
      ? data.filter((item: any) => item.is_deleted === false || item.is_deleted === null)
      : [];

    console.log("🔍 Filtered products (is_deleted=false):", filteredData.length);
    console.log("📋 Filtered products data:", filteredData);
    return filteredData;

  } catch (error) {
    console.error("Error fetching products:", error);
    return [];
  }
}

export async function updateProduct(id: string, updates: any) {
  const res = await fetch(`http://localhost:5000/api/product/${id}`, {
    method: "PUT",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify(updates),
  });
  if (!res.ok) throw new Error("Failed to update product");
  return res.json();
}

export async function deleteProduct(id: string) {
  const token = (await cookies()).get("token")?.value;
  
  if (token) {
    await fetch(`http://localhost:5000/api/product/softdelete/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: "Bearer " + token,
      },
    });
  }
}
