"use server";

import axios from "axios";
import { cookies } from "next/headers";

const myApi = process.env.MY_API;

const httpClient = axios.create({
    baseURL: myApi,
    withCredentials: true,
});

httpClient.interceptors.request.use(async (config) => {
    const token = (await cookies()).get("token")?.value;
    if(token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
});


export default httpClient;
