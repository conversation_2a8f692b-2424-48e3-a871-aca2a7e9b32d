import React from 'react';
import { X } from 'lucide-react';

type DeleteConfirmationModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  orderIdToDelete: string | null;
};

export const DeleteConfirmationModal: React.FC<DeleteConfirmationModalProps> = ({ isOpen, onClose, onConfirm, orderIdToDelete }) => {
  if (!isOpen || !orderIdToDelete) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Background overlay with transition */}
      <div
        className="absolute inset-0 bg-black/40 transition-opacity duration-300 ease-in-out"
        aria-hidden="true"
      />
      {/* Modal content with transition */}
      <div className="relative bg-white rounded-lg shadow-xl p-6 w-full max-w-md animate-modal-fade transform transition-all duration-300 ease-in-out scale-100 opacity-100">
        <div className="flex justify-between items-start mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Confirm Deletion</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors p-1 -mt-1 -mr-1">
            <X className="w-5 h-5" />
          </button>
        </div>
        <p className="text-sm text-gray-600 mb-6">
          Are you sure you want to delete order <span className="font-medium text-gray-800">{orderIdToDelete}</span>? This action cannot be undone.
        </p>
        <div className="flex justify-end space-x-3">
          <button onClick={onClose} className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">Cancel</button>
          <button onClick={onConfirm} className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors">Yes, Delete</button>
        </div>
      </div>
      <style jsx>{`
        .animate-modal-fade {
          animation: modal-fade-in 0.3s cubic-bezier(0.4,0,0.2,1);
        }
        @keyframes modal-fade-in {
          0% { opacity: 0; transform: scale(0.95); }
          100% { opacity: 1; transform: scale(1); }
        }
      `}</style>
    </div>
  );
};
