import React from 'react';
import { <PERSON>, FileText, Trash2, MoreHorizontal } from 'lucide-react';
import type { Order, OrderStatus } from '../app/(protected)/dashboard/page'; // Assuming types are exported

type OrderCardListProps = {
  orders: Order[];
  highlightedOrderId: string | null;
  getStatusColorClass: (status: OrderStatus) => string;
  onDeleteOrder: (orderId: string) => void;
  onEditOrder?: (order: Order) => void;
  // onViewOrder: (orderId: string) => void; // Future enhancement
};

export const OrderCardList: React.FC<OrderCardListProps> = ({ orders, highlightedOrderId, getStatusColorClass, onDeleteOrder, onEditOrder }) => {
  if (orders.length === 0) {
    return <div className="lg:hidden p-4 text-center text-gray-500">No orders found.</div>;
  }

  return (
    <div className="lg:hidden">
      <div className="divide-y divide-gray-200">
        {orders.map((order) => (
          <div key={order.id} className={`p-4 hover:bg-gray-50 transition-all duration-500 ease-in-out ${order.id === highlightedOrderId ? 'bg-green-100' : ''}`}>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-900">{order.id}</span>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColorClass(order.status)}`}>{order.status}</span>
            </div>
            <div className="flex items-center mb-2">
              <div className="w-6 h-6 bg-gray-200 rounded-full flex items-center justify-center mr-2 flex-shrink-0">
                <span className="text-xs font-medium text-gray-600">{order.customer.split(' ').map(n => n[0]).join('')}</span>
              </div>
              <span className="text-sm text-gray-900 truncate">{order.customer}</span>
            </div>
            <div className="flex items-center justify-between text-sm text-gray-500">
              <span>{order.date}</span>
              <span className="font-medium text-gray-900">${order.amount.toFixed(2)}</span>
            </div>
            <div className="flex items-center justify-end space-x-1 mt-2">
              <button className="p-1 hover:bg-gray-100 rounded" title="View"><Eye className="w-4 h-4" /></button>
              <button className="p-1 hover:bg-gray-100 rounded" title="Invoice"><FileText className="w-4 h-4" /></button>
              <button onClick={() => onDeleteOrder(order.id)} className="p-1 hover:bg-gray-100 rounded" title="Delete"><Trash2 className="w-4 h-4 text-red-500" /></button>
              {onEditOrder && (
                <button onClick={() => onEditOrder(order)} className="p-1 hover:bg-gray-100 rounded" title="Edit"><MoreHorizontal className="w-4 h-4" /></button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};