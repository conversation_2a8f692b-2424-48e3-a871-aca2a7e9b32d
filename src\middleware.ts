

import { cookies } from "next/headers";
import { NextResponse } from "next/server";

const publicRoutes = ["/login", "/register", "/welcome"];

export async function middleware(request: any) {
  const path = request.nextUrl.pathname;
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const isPublicRoute = publicRoutes.includes(path);

  if (!isPublicRoute && !token) {
    return NextResponse.redirect(new URL("/welcome", request.url));
  }

    if (isPublicRoute && token) {
    return NextResponse.redirect(new URL("/dashboard", request.nextUrl));
  }


  return NextResponse.next();
}

export const config = {
  matcher: ["/login", "/register", "/dashboard/:path*"],
};