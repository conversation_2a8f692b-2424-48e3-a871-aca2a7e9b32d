"use client"
import { useEffect, useState } from "react";
import { Search, X } from "lucide-react";
import type { TabName } from "../app/(protected)/dashboard/page";
import { useRouter } from 'next/navigation';

type TabDefinition = {
  label: TabName | string;
  count: number;
};

type Order = {
  id: string;
  customer: string;
  status: string;
  amount: number;
  date: string;
};

type OrderListControlsProps = {
  tabs: TabDefinition[];
  activeTab: TabName | string;
  onTabClick: (tabLabel: TabName) => void;
  searchTerm: string;
  onSearchChange: (term: string) => void;
  orders: Order[];
};

export const OrderListControls: React.FC<OrderListControlsProps> = ({
  tabs,
  activeTab,
  onTabClick,
  orders,
}) => {
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [modalSearch, setModalSearch] = useState("");
  const [modalResults, setModalResults] = useState<Order[]>([]);
  const router = useRouter();

  useEffect(() => {
    if (modalSearch.trim() === "") {
      setModalResults([]);
    } else {
      setModalResults(
        orders.filter(order =>
          order.customer.toLowerCase().includes(modalSearch.toLowerCase())
        )
      );
    }
  }, [modalSearch, orders]);

  return (
    <div className="p-4 lg:p-6 border-b border-gray-200">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">Order List</h2>
      <div className="mb-4">
        <div className="flex overflow-x-auto gap-2 items-center w-full no-scrollbar pb-1">
          {tabs.map((tab) => (
            <button
              key={tab.label}
              onClick={() => onTabClick(tab.label as TabName)}
              className={`px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium rounded-lg transition-colors whitespace-nowrap flex-shrink-0 ${
                activeTab === tab.label
                  ? "bg-blue-600 text-white"
                  : "text-gray-600 hover:bg-gray-100"
              }`}
              style={{ minWidth: '80px' }}
            >
              <span className="hidden sm:inline">
                {tab.label} ({tab.count})
              </span>
              <span className="sm:hidden">{tab.label}</span>
            </button>
          ))}
          <button
            className="flex items-center justify-center w-9 h-9 sm:w-10 sm:h-10 rounded-lg border border-gray-300 hover:bg-gray-100 transition-colors ml-auto flex-shrink-0"
            onClick={() => setIsSearchModalOpen(true)}
            aria-label="Search"
            type="button"
          >
            <Search className="w-5 h-5 text-gray-500" />
          </button>
        </div>
      </div>
      {/* Search Modal */}
      {isSearchModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/40 transition-opacity duration-300" aria-hidden="true" onClick={() => setIsSearchModalOpen(false)} />
          <div className="relative bg-white rounded-lg w-full max-w-md shadow-lg p-6 animate-modal-fade">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900">Search Orders</h3>
              <button onClick={() => setIsSearchModalOpen(false)} className="text-gray-400 hover:text-gray-600 transition-colors p-1">
                <X className="w-6 h-6" />
              </button>
            </div>
            <input
              type="text"
              autoFocus
              placeholder="Search..."
              value={modalSearch}
              onChange={e => setModalSearch(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none mb-4"
            />
            <div>
              {modalSearch.trim() === "" && <div className="text-gray-400 text-sm">Type to search...</div>}
              {modalResults.length > 0 ? (
                <ul className="divide-y divide-gray-100">
                  {modalResults.map((order, idx) => (
                    <li key={order.id} className="py-2 text-gray-800 cursor-pointer hover:bg-blue-50 rounded transition-colors"
                      onClick={() => {
                        setIsSearchModalOpen(false);
                        router.push(`/orders/${order.id.replace('#', '')}`);
                      }}
                    >
                      <div className="font-semibold">{order.customer}</div>
                      <div className="text-xs text-gray-500">Order ID: {order.id} | Status: {order.status} | Amount: ${order.amount}</div>
                    </li>
                  ))}
                </ul>
              ) : modalSearch.trim() !== "" ? (
                <div className="text-gray-400 text-sm">No results found.</div>
              ) : null}
            </div>
            <style jsx>{`
              .animate-modal-fade {
                animation: modal-fade-in 0.3s cubic-bezier(0.4,0,0.2,1);
              }
              @keyframes modal-fade-in {
                0% { opacity: 0; transform: scale(0.95); }
                100% { opacity: 1; transform: scale(1); }
              }
            `}</style>
          </div>
        </div>
      )}
      <style>{`
        .no-scrollbar::-webkit-scrollbar { display: none; }
        .no-scrollbar { -ms-overflow-style: none; scrollbar-width: none; }
      `}</style>
    </div>
  );
};
