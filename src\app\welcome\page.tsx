"use client";
import React from "react";
import Link from "next/link";

export default function WelcomePage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="bg-white p-8 rounded-lg shadow-sm w-full max-w-md border border-gray-200 flex flex-col items-center">
        <h1 className="text-3xl font-bold mb-4 text-center">WareHous Dashboard</h1>
        <p className="text-gray-600 mb-8 text-center">A modern and stylish panel for order and stock management.</p>
        <div className="flex flex-col gap-4 w-full">
          <Link href="/login" className="w-full">
            <button className="w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700 transition text-lg font-semibold">
              Login
            </button>
          </Link>
          <Link href="/register" className="w-full">
            <button className="w-full bg-gray-100 text-blue-700 py-2 rounded hover:bg-blue-200 transition text-lg font-semibold border border-blue-200">
              Register
            </button>
          </Link>
        </div>
      </div>
    </div>
  );
}
