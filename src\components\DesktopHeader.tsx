import React from 'react';
import { Plus } from 'lucide-react';
import type { Sidebar<PERSON><PERSON> } from '../app/(protected)/dashboard/page'; // Assuming SidebarKey is exported
import LogoutButton from './LogoutButton';

type DesktopHeaderProps = {
  activeContent: SidebarKey | string;
  showCreateOrderButton: boolean;
  onCreateOrderClick: () => void;
};

export const DesktopHeader: React.FC<DesktopHeaderProps> = ({ activeContent, showCreateOrderButton, onCreateOrderClick }) => {
  return (
    <div className="hidden lg:flex items-center justify-between mb-8">
      <h1 className="text-2xl font-bold text-gray-900">{activeContent}</h1>
      <div className="flex items-center gap-4">
        {showCreateOrderButton && (
          <button onClick={onCreateOrderClick} className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
            <Plus className="w-4 h-4" />
            <span>Create New Order</span>
          </button>
        )}
      </div>
    </div>
  );
};