import React from 'react';
import { TrendingUp, TrendingDown } from 'lucide-react';

export type StatCardProps = {
  title: string;
  value: number | string; // Allow string for cases like formatted numbers or "N/A"
  change: string;
  isPositive: boolean;
  color: string; // Tailwind CSS background color class e.g., 'bg-blue-100'
  icon?: React.ReactNode; // Optional: if you want to pass a specific icon
};

export const StatCard: React.FC<StatCardProps> = ({ title, value, change, isPositive, color }) => (
  <div className="bg-white rounded-lg p-4 lg:p-6 shadow-sm border border-gray-200">
    <div className="flex items-center justify-between">
      <div className="flex-1 min-w-0">
        <p className="text-xs sm:text-sm font-medium text-gray-600 truncate">{title}</p>
        <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1 sm:mt-2">{value}</p>
        <div className="flex items-center mt-1 sm:mt-2">
          {isPositive ? <TrendingUp className="w-3 h-3 sm:w-4 sm:h-4 text-green-500 flex-shrink-0" /> : <TrendingDown className="w-3 h-3 sm:w-4 sm:h-4 text-red-500 flex-shrink-0" />}
          <span className={`text-xs sm:text-sm ml-1 ${isPositive ? 'text-green-600' : 'text-red-600'}`}>{change}</span>
          <span className="text-xs sm:text-sm text-gray-500 ml-1 hidden sm:inline">Last Month</span>
        </div>
      </div>
      <div className={`w-8 h-8 sm:w-12 sm:h-12 rounded-lg ${color} flex items-center justify-center flex-shrink-0 ml-2`}>
        {/* Simplified decorative element, or pass icon prop here */}
        <div className="w-4 h-4 sm:w-6 sm:h-6 bg-white rounded opacity-20"></div>
      </div>
    </div>
  </div>
);