import React, { useState } from 'react';
import type { NewProductFormData } from './CreateProductModal';
import { PaginationControls } from './PaginationControls';


interface ProductTableProps {
  products: NewProductFormData[];
  onDeleteProduct: (productId: string) => void;
}


const ITEMS_PER_PAGE = 10;


export const ProductTable: React.FC<ProductTableProps> = ({ products, onDeleteProduct }) => {
  const [currentPage, setCurrentPage] = useState(1);
  const [deletingId, setDeletingId] = useState<string | null>(null);
  const totalPages = Math.ceil(products.length / ITEMS_PER_PAGE);
  const paginatedProducts = products.slice((currentPage - 1) * ITEMS_PER_PAGE, currentPage * ITEMS_PER_PAGE);

  if (products.length === 0) {
    return (
      <div className="hidden lg:block overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Block #</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Piece #</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Color</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model ID</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Model Name</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td colSpan={13} className="px-6 py-4 text-center text-gray-500">No products found.</td>
            </tr>
          </tbody>
        </table>
      </div>
    );
  }

  return (
    <div className="lg:block overflow-x-auto">
      <table className="w-full">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">#</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product Name</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Thumbnail</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Size</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Block #</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Piece #</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Color</th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {paginatedProducts.map((product: any, idx: number) => (
            <tr key={product.productId ?? idx} className="hover:bg-gray-50 transition-all duration-500 ease-in-out">
              <td className="px-4 py-4 whitespace-nowrap text-sm text-gray-500">
                {(currentPage - 1) * ITEMS_PER_PAGE + idx + 1}
              </td>
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{product.productName}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"><img src={product.thumbnail} alt="" width={60} height={50}/></td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">${product.price}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.size}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.stock}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.blockNumber}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.pieceNumber}</td>
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{product.color}</td>
              <td className="px-6 py-4 whitespace-nowrap">
                <button
                  className="text-red-600 hover:underline"
                  disabled={deletingId === product.productId}
                  onClick={() => {
                    setDeletingId(product.productId);
                    onDeleteProduct(product.productId);
                    console.log(product.productId)
                  
                    setDeletingId(null);
                  }}
                  title="Delete"
                >
                  {deletingId === product.productId ? "Deleting..." : "Delete"}
                </button>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
      <PaginationControls
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={setCurrentPage}
        totalResults={products.length}
        itemsPerPage={ITEMS_PER_PAGE}
      />
    </div>
  );
};
