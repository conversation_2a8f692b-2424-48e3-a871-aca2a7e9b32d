import React, { ChangeEvent, FC, FormEvent } from "react";
import { X } from "lucide-react";

export type NewProductFormData = {
  productId: string;
  productName: string;
  price: number;
  size: string;
  thumbnail: string;
  stock: number;
  blockNumber: number;
  pieceNumber: number;
  color: string;
  productModelId: number;
};

type CreateProductModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (e: FormEvent<HTMLFormElement>) => void;
  formData: NewProductFormData;
  onInputChange: (
    e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>
  ) => void;
};

export const CreateProductModal: FC<CreateProductModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  formData,
  onInputChange,
}) => {
  if (!isOpen) return null;

  // const [state, action, loading] = useActionState(createProductAction, null);

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div
        className="absolute inset-0 bg-black/40 transition-opacity duration-300 ease-in-out"
        aria-hidden="true"
      />
      <div className="relative bg-white rounded-lg w-full max-w-2xl max-h-[90vh] flex flex-col shadow-lg transform transition-all duration-300 ease-in-out scale-100 opacity-100 animate-modal-fade">
        <div className="sticky top-0 bg-white p-4 lg:p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg lg:text-xl font-bold text-gray-900">
              Create New Product
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors p-1"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>
        <form
          id="createProductFormInModal"
          onSubmit={onSubmit}
          className="p-4 lg:p-6 space-y-4 lg:space-y-6 overflow-y-auto flex-grow"
        >
          <div className="grid grid-cols-1">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Name *
              </label>
              <input
                type="text"
                name="productName"
                value={formData.productName}
                onChange={onInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none"
                placeholder="Enter product name"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price *
              </label>
              <input
                type="number"
                name="price"
                value={formData.price}
                onChange={onInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none"
                placeholder="Enter price"
              />
            </div>
            {/* <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Quantity *</label>
              <input type="number" name="quantity" value={formData.quantity} onChange={onInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none" placeholder="Enter quantity"/>
            </div> */}
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Size *
              </label>
              <input
                type="text"
                name="size"
                value={formData.size}
                onChange={onInputChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none"
                placeholder="e.g., S, M, L, XL or 38, 40, 42"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thumbnail URL
              </label>
              <input
                type="file"
                name="thumbnail"
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none"
                placeholder="Enter thumbnail URL"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Stock
              </label>
              <input
                type="number"
                name="stock"
                value={formData.stock}
                onChange={onInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none"
                placeholder="Enter stock"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Block Number
              </label>
              <input
                type="number"
                name="blockNumber"
                value={formData.blockNumber}
                onChange={onInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none"
                placeholder="Enter block number"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Piece Number
              </label>
              <input
                type="number"
                name="pieceNumber"
                value={formData.pieceNumber}
                onChange={onInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none"
                placeholder="Enter piece number"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Color
              </label>
              <input
                type="text"
                name="color"
                value={formData.color}
                onChange={onInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none"
                placeholder="Enter color"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Model ID
              </label>
              <input
                type="number"
                name="productModelId"
                value={formData.productModelId}
                onChange={onInputChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none"
                placeholder="Enter product model id"
              />
            </div>
          </div>
          <div className="flex justify-end gap-2 pt-2">
            <button
              type="submit"
              onClick={onClose}
              className="px-4 py-2 rounded bg-gray-100 text-gray-700 hover:bg-gray-200 transition"
            >
              Cancel
            </button>

            <button
              type="submit"
              className="px-4 py-2 rounded bg-blue-600 text-white hover:bg-blue-700 transition"
            >
              Create Product
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};
