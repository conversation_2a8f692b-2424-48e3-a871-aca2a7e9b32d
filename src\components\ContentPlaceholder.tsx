import { FC, ReactNode } from "react";

type ContentPlaceholderProps = {
  title: string;
  type: 'dashboard' | 'settings' | 'generic' | 'product';
  children?:ReactNode;
};

export const ContentPlaceholder: FC<ContentPlaceholderProps> = ({ title, type, children }) => {
  return (
    <div className="bg-white rounded-lg p-6 shadow-sm border border-gray-200">
      <h2 className="text-xl font-bold text-gray-800 mb-4">{title}</h2>
      {type === 'dashboard' && (
        <>
          <p className="text-gray-600">This is a placeholder for your main dashboard content. You can add various widgets, charts, and key performance indicators here.</p>
          <p className="text-gray-600 mt-2">Feel free to expand this section with actual data visualizations and summaries relevant to your FlowStock operations.</p>
        </>
      )}
      {type === 'settings' && (
        <>
          <p className="text-gray-600">This is the settings page. Here you can imagine various configuration options for the dashboard, user profiles, notifications, etc.</p>
          {children}
        </>
      )}
      {type === 'generic' && (
        <p className="text-gray-600">This page is under construction. Please check back later for content related to {title}.</p>
      )}
      {type === 'product' && (
        <p className="text-gray-600">Welcome to Product!</p>
      )}
    </div>
  );
};