import React, { useState } from 'react';
import { Search, X } from 'lucide-react';

interface ProductListControlsProps {
  searchTerm: string;
  onSearchChange: (term: string) => void;
}

export const ProductListControls: React.FC<ProductListControlsProps> = ({ searchTerm, onSearchChange }) => {
  const [isSearchModalOpen, setIsSearchModalOpen] = useState(false);
  const [modalSearch, setModalSearch] = useState("");

  React.useEffect(() => {
    if (isSearchModalOpen) setModalSearch(searchTerm);
  }, [isSearchModalOpen, searchTerm]);

  const handleModalSearch = (val: string) => {
    setModalSearch(val);
    onSearchChange(val);
  };

  return (
    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 px-4 py-2 border-b border-gray-100 bg-gray-50 rounded-t-lg relative">
      <div className="flex-1 flex items-center gap-2">
        <button
          className="flex items-center justify-center w-9 h-9 rounded-lg border border-gray-300 hover:bg-gray-100 transition-colors"
          onClick={() => setIsSearchModalOpen(true)}
          aria-label="Search"
          type="button"
        >
          <Search className="w-5 h-5 text-gray-500" />
        </button>
      </div>
      {isSearchModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          <div className="absolute inset-0 bg-black/40 transition-opacity duration-300" aria-hidden="true" onClick={() => setIsSearchModalOpen(false)} />
          <div className="relative bg-white rounded-lg w-full max-w-md shadow-lg p-6 animate-modal-fade">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-gray-900">Search Products</h3>
              <button onClick={() => setIsSearchModalOpen(false)} className="text-gray-400 hover:text-gray-600 transition-colors p-1">
                <X className="w-6 h-6" />
              </button>
            </div>
            <input
              type="text"
              autoFocus
              placeholder="Search product name, category, color..."
              value={modalSearch}
              onChange={e => handleModalSearch(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:outline-none mb-2"
            />
            <style jsx>{`
              .animate-modal-fade {
                animation: modal-fade-in 0.3s cubic-bezier(0.4,0,0.2,1);
              }
              @keyframes modal-fade-in {
                0% { opacity: 0; transform: scale(0.95); }
                100% { opacity: 1; transform: scale(1); }
              }
            `}</style>
          </div>
        </div>
      )}
    </div>
  );
};
