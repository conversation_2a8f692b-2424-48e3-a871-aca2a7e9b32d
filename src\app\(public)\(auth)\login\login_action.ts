"use server";

import { cookies } from "next/headers";

export async function login(_: unknown, formData: FormData) {
  const response = await fetch("http://localhost:5000/api/auth/login", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      email: formData.get("email"),
      password: formData.get("password"),
    }),
  });

  let data = null;
  try {
    data = await response.json();
  } catch (e) {
    return "Invalid email or password";
  }

  if (!response.ok || !data || !data.token) {
    return (data && data.message) ? data.message : "Invalid email or password";
  }

  const cookieStore = await cookies();
  cookieStore.set("token", data.token, {
    httpOnly: true,
    path: "/",
    secure: true,
    sameSite: "strict",
    // maxAge: 60 * 60 * 24 * 3,
  });
  return "ok";
}
