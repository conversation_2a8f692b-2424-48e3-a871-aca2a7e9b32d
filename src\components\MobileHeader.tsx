import React from 'react';
import { Menu, Plus } from 'lucide-react';
import type { Sidebar<PERSON>ey } from '../app/(protected)/dashboard/page'; // Assuming SidebarKey is exported

type MobileHeaderProps = {
  onOpenSidebar: () => void;
  activeContent: SidebarKey | string; // Allow string for flexibility
  showCreateOrderButton: boolean;
  onCreateOrderClick: () => void;
};

export const MobileHeader: React.FC<MobileHeaderProps> = ({ onOpenSidebar, activeContent, showCreateOrderButton, onCreateOrderClick }) => {
  return (
    <div className="lg:hidden bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
      <button onClick={onOpenSidebar} className="p-2 text-gray-500 hover:text-gray-700">
        <Menu className="w-5 h-5" />
      </button>
      <h1 className="text-lg font-semibold text-gray-900">{activeContent}</h1>
      {showCreateOrderButton ? (
        <button onClick={onCreateOrderClick} className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg">
          <Plus className="w-4 h-4" />
        </button>
      ) : <div className="w-8 h-8"></div>}
    </div>
  );
};