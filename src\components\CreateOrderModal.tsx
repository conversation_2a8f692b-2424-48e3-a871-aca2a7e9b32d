import React from 'react';
import { X } from 'lucide-react';
import type { OrderStatus } from '../app/(protected)/dashboard/page'; 
import { ORDER_STATUS } from '../app/(protected)/dashboard/page';

export type NewOrderFormData = {
  customerName: string;
  customerEmail: string;
  productName: string;
  price: string;
  quantity: string;
  category: string;
  description: string;
  status: OrderStatus;
};

type CreateOrderModalProps = {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (e: React.FormEvent<HTMLFormElement>) => void;
  formData: NewOrderFormData;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => void;
};

export const CreateOrderModal: React.FC<CreateOrderModalProps> = ({ isOpen, onClose, onSubmit, formData, onInputChange }) => {
  if (!isOpen) return null;

  const totalAmount = formData.price && formData.quantity ? (parseFloat(formData.price) * parseInt(formData.quantity)).toFixed(2) : '0.00';

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Background overlay with transition */}
      <div
        className="absolute inset-0 bg-black/40 transition-opacity duration-300 ease-in-out"
        aria-hidden="true"
      />
      {/* Modal content with transition */}
      <div
        className="relative bg-white rounded-lg w-full max-w-2xl max-h-[90vh] flex flex-col shadow-lg transform transition-all duration-300 ease-in-out scale-100 opacity-100 animate-modal-fade"
      >
        <div className="sticky top-0 bg-white p-4 lg:p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h2 className="text-lg lg:text-xl font-bold text-gray-900">Create New Order</h2>
            <button onClick={onClose} className="text-gray-400 hover:text-gray-600 transition-colors p-1">
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        <form id="createOrderFormInModal" onSubmit={onSubmit} className="p-4 lg:p-6 space-y-4 lg:space-y-6 overflow-y-auto flex-grow">
          {/* Customer Information */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Customer Name *</label>
              <input type="text" name="customerName" value={formData.customerName} onChange={onInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none" placeholder="Enter customer name"/>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Customer Email</label>
              <input type="email" name="customerEmail" value={formData.customerEmail} onChange={onInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none" placeholder="Enter customer email"/>
            </div>
          </div>

          {/* Product Information */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Product Name *</label>
              <input type="text" name="productName" value={formData.productName} onChange={onInputChange} required className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none" placeholder="Enter product name"/>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
              <select name="category" value={formData.category} onChange={onInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none">
                <option value="">Select category</option>
                <option value="Electronics">Electronics</option>
                <option value="Clothing">Clothing</option>
                <option value="Books">Books</option>
                <option value="Home & Garden">Home & Garden</option>
                <option value="Sports">Sports</option>
                <option value="Other">Other</option>
              </select>
            </div>
          </div>

          {/* Price and Quantity */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Unit Price ($) *</label>
              <input type="number" name="price" value={formData.price} onChange={onInputChange} required min="0" step="0.01" className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none" placeholder="0.00"/>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Quantity *</label>
              <input type="number" name="quantity" value={formData.quantity} onChange={onInputChange} required min="1" className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none" placeholder="1"/>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Total Amount</label>
              <div className="w-full px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-700">${totalAmount}</div>
            </div>
          </div>

          {/* Order Status */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Order Status</label>
            <select name="status" value={formData.status} onChange={onInputChange} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none">
              <option value={ORDER_STATUS.PENDING}>Pending</option>
              <option value={ORDER_STATUS.COMPLETED}>Completed</option>
            </select>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
            <textarea name="description" value={formData.description} onChange={onInputChange} rows={3} className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent focus:outline-none resize-none" placeholder="Enter order description (optional)"/>
          </div>
        </form>

        {/* Form Actions */}
        <div className="sticky bottom-0 bg-white p-4 border-t border-gray-200 flex flex-col sm:flex-row items-center justify-end gap-3">
          <button type="button" onClick={onClose} className="w-full sm:w-auto px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
            Cancel
          </button>
          <button type="submit" form="createOrderFormInModal" className="w-full sm:w-auto px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors">
            Create Order
          </button>
        </div>
      </div>
      <style jsx>{`
        .animate-modal-fade {
          animation: modal-fade-in 0.3s cubic-bezier(0.4,0,0.2,1);
        }
        @keyframes modal-fade-in {
          0% { opacity: 0; transform: scale(0.95); }
          100% { opacity: 1; transform: scale(1); }
        }
      `}</style>
    </div>
  );
};
