"use server";

export async function register(_: unknown, formData: FormData) {
  const response = await fetch("http://localhost:5000/api/auth/register", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      fullname: formData.get("fullname"),
      email: formData.get("email"),
      password: formData.get("password"),
    }),
  });

  if (response.ok) return "ok";
  return "fail";
}