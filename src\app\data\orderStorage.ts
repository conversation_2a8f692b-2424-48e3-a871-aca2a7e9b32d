import { Order } from './fakeOrders';

const STORAGE_KEY = 'orders_data';

export function getOrders(): Order[] {
  if (typeof window !== 'undefined') {
    const stored = localStorage.getItem(STORAGE_KEY);
    if (stored) {
      try {
        return JSON.parse(stored);
      } catch {
        return fakeOrders;
      }
    }
  }
  return fakeOrders;
}

export function saveOrders(orders: Order[]) {
  if (typeof window !== 'undefined') {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(orders));
  }
}

export function updateOrder(updated: Order) {
  const orders = getOrders();
  const newOrders = orders.map(o => o.id === updated.id ? updated : o);
  saveOrders(newOrders);
}
