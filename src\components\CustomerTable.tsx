import React from "react";

export type Customer = {
  id: string;
  name: string;
  phone: string;
};

interface CustomerTableProps {
  customers: Customer[];
}

export const CustomerTable: React.FC<CustomerTableProps> = ({ customers }) => {
  if (!Array.isArray(customers) || customers.length === 0) {
    return <div className="text-center text-gray-500 py-8">No customers found.</div>;
  }
  return (
    <div className="overflow-x-auto">
      <table className="min-w-full bg-white border border-gray-200 rounded-lg">
        <thead>
          <tr className="bg-gray-100 text-gray-700">
            <th className="px-6 py-3 text-left font-semibold">Name</th>
            <th className="px-6 py-3 text-left font-semibold">Phone</th>
          </tr>
        </thead>
        <tbody>
          {customers.map((customer) => (
            <tr key={customer.id} className="border-t hover:bg-gray-50">
              <td className="px-6 py-4">{customer.name}</td>
              <td className="px-6 py-4">{customer.phone}</td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
