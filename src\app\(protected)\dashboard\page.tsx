"use client";
import React, { useState, useEffect, SetStateAction, Dispatch, ChangeEvent, FormEvent } from 'react';
import { 
  LayoutDashboard, 
  ShoppingCart, 
  Users, 
  CreditCard, 
  Package, 
  BarChart3, 
  UserCog, 
  Settings } from 'lucide-react';
  
import { getProducts, deleteProduct } from "./product_action";
import toast, { Toaster} from 'react-hot-toast';
import { Sidebar } from '@/components/Sidebar';
import { CreateOrderModal } from '@/components/CreateOrderModal';
import { CreateProductModal } from '@/components/CreateProductModal';
import { ProductTable } from '@/components/ProductTable';
import { OrderTable } from '@/components/OrderTable';
import { PaginationControls } from '@/components/PaginationControls';
import { NewOrderFormData } from '@/components/CreateOrderModal';
import { NewProductFormData } from '@/components/CreateProductModal';
import { OrderCardList } from '@/components/OrderCardList';
import { MobileHeader } from '@/components/MobileHeader';
import { DesktopHeader } from '@/components/DesktopHeader';
import { OrderListControls } from '@/components/OrderListControl';
import { ProductListControls } from '@/components/ProductListControls';
import { DeleteConfirmationModal } from '@/components/DeleteConfirmationModel';
import { createProduct } from './actions';
  const ITEMS_PER_PAGE = 8;

export const ORDER_STATUS = {
  COMPLETED: 'Completed',
  PENDING: 'Pending',
  CANCELLED: 'Cancelled',
} as const;

export type OrderStatus = typeof ORDER_STATUS[keyof typeof ORDER_STATUS];

export const TAB_NAMES = {
  TOTAL: 'Total',
  COMPLETED: 'Completed',
  PENDING: 'Pending',
} as const;

export type TabName = typeof TAB_NAMES[keyof typeof TAB_NAMES];

export type Order = {
  id: string;
  customer: string;
  status: OrderStatus;
  amount: number;
  date: string;
  productName?: string;
  price?: number;
  quantity?: number;
  category?: string;
  description?: string;
  customerEmail?: string;
};  

const sidebarItemsDefinition = [
  { icon: LayoutDashboard, label: 'Dashboard', key: 'Dashboard' as const },
  { icon: ShoppingCart, label: 'Orders', key: 'Orders' as const },
  { icon: Users, label: 'Customers', key: 'Customers' as const },
  { icon: CreditCard, label: 'Payments', key: 'Payments' as const },
  { icon: Package, label: 'Products', key: 'Products' as const },
  { icon: BarChart3, label: 'Analytics', key: 'Analytics' as const },
  { icon: UserCog, label: 'Users', key: 'Users' as const },
  { icon: Settings, label: 'Settings', key: 'Settings' as const },
];
export type SidebarKey = typeof sidebarItemsDefinition[number]['key'];

const STATUS_CLASSES: Record<OrderStatus, string> = {
    [ORDER_STATUS.COMPLETED]: 'bg-green-100 text-green-800',
    [ORDER_STATUS.PENDING]: 'bg-yellow-100 text-yellow-800',
    [ORDER_STATUS.CANCELLED]: 'bg-red-100 text-red-800',
  };

  const getStatusColorClass = (status: OrderStatus) => {
    return STATUS_CLASSES[status] || 'bg-gray-100 text-gray-800';
  };

const getEmptyOrder = (): NewOrderFormData => ({
  customerName: '',
  customerEmail: '',
  productName: '',
  price: '',
  quantity: '',
  category: '',
  description: '',
  status: ORDER_STATUS.PENDING
});

const getEmptyProduct = (): NewProductFormData => ({
  productId: '',
  productName: '',
  price: 0,
  size: '',
  thumbnail: '',
  stock: 0,
  blockNumber: 0,
  pieceNumber: 0,
  color: '',
  productModelId: 0,
});

const handleInputChangeGeneric = <T,>(setter: Dispatch<SetStateAction<T>>) =>
  (e: ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setter(prev => ({
      ...prev,
      [name]: name === 'status' ? value as OrderStatus : value
    }));
  };

const API = process.env.NEXT_PUBLIC_MY_API;

const FlowStockDashboard = () => {
  const [activeTab, setActiveTab] = useState<TabName>(TAB_NAMES.TOTAL);
  const [searchTerm, setSearchTerm] = useState('');
  const [orders, setOrders] = useState<Order[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [activeContent, setActiveContent] = useState<SidebarKey>('Orders');
  const [highlightedOrderId, setHighlightedOrderId] = useState<string | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [orderToDeleteId, setOrderToDeleteId] = useState<string | null>(null);
  const [newOrder, setNewOrder] = useState<NewOrderFormData>({
    customerName: '',
    customerEmail: '',
    productName: '',
    price: '',
    quantity: '',
    category: '',
    description: '',
    status: ORDER_STATUS.PENDING
  });
  const [currentPage, setCurrentPage] = useState(1);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);  
  const [orderToEdit, setOrderToEdit] = useState<Order | null>(null);
  const [isProductModalOpen, setIsProductModalOpen] = useState(false);
  const [newProduct, setNewProduct] = useState<NewProductFormData>(getEmptyProduct());
  const [products, setProducts] = useState<NewProductFormData[]>([]);
  const [productSearchTerm, setProductSearchTerm] = useState('');
  const [showProductSearch, setShowProductSearch] = useState(false);
  const [productToDeleteId, setProductToDeleteId] = useState<string | null>(null);
  const [isProductDeleteModalOpen, setIsProductDeleteModalOpen] = useState(false);

  const searchParams = typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : null;

  useEffect(() => {
    if (searchParams) {
      const highlight = searchParams.get('highlight');
      if (highlight) {
        setHighlightedOrderId(highlight);
        // Highlight
        setTimeout(() => setHighlightedOrderId(null), 3000);
      }
    }
  }, []);

  useEffect(() => {
    const fetchOrders = async () => {
      try {
        const res = await fetch(`${API}/api/order`);
        const data = await res.json();
        const mapped = Array.isArray(data)
          ? data.map((item: any) => ({
              id: item.id?.toString() || '',
              customer: item.customer || item.customerName || '',
              status: item.status || ORDER_STATUS.PENDING,
              amount: item.amount || 0,
              date: item.date || new Date().toLocaleDateString('en-GB', { day: '2-digit', month: 'short', year: 'numeric' }),
              productName: item.productName || '',
              price: item.price || '',
              quantity: item.quantity || '',
              category: item.category || '',
              description: item.description || '',
              customerEmail: item.customerEmail || ''
            }))
          : [];
        setOrders(mapped);
      } catch {
        setOrders([]);
      }
    };
    fetchOrders();
  }, []);

  useEffect(() => {
    let isMounted = true;
    const fetchProducts = async () => {
      try {
        const data = await getProducts();
        const mapped = Array.isArray(data)
          ? data.map((item: any) => ({
              productId: item.id?.toString() ?? '',
              productName: item.productName || '',
              price: item.price || '',
              size: item.size || '',
              thumbnail: item.thumbnail || '',
              stock: item.stock || '',
              blockNumber: item.blockNumber || '',
              pieceNumber: item.pieceNumber || '',
              color: item.color || '',
              productModelId: item.productModelId || '',
            }))
          : [];
        if (isMounted) setProducts(mapped);
      } catch {
        if (isMounted) setProducts([]);
      }
    };
    fetchProducts();
    return () => { isMounted = false; };
  }, []);

  const allFilteredOrders = React.useMemo(() => {
    return orders.filter(order => {
      const matchesSearch = order.customer.toLowerCase().includes(searchTerm.toLowerCase()) || 
                            order.id.toLowerCase().includes(searchTerm.toLowerCase());
      if (activeTab === TAB_NAMES.TOTAL) return matchesSearch;
      if (activeTab === TAB_NAMES.COMPLETED) return matchesSearch && order.status === ORDER_STATUS.COMPLETED;
      if (activeTab === TAB_NAMES.PENDING) return matchesSearch && order.status === ORDER_STATUS.PENDING;
      return matchesSearch; 
    });
  }, [orders, searchTerm, activeTab]);

  const totalPages = React.useMemo(() => Math.ceil(allFilteredOrders.length / ITEMS_PER_PAGE), [allFilteredOrders.length]);

  const paginatedOrders = React.useMemo(() => {
    const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
    const endIndex = startIndex + ITEMS_PER_PAGE;
    return allFilteredOrders.slice(startIndex, endIndex);
  }, [allFilteredOrders, currentPage]);

  useEffect(() => {
    const newTotalPages = Math.ceil(allFilteredOrders.length / ITEMS_PER_PAGE);
    if (allFilteredOrders.length === 0) {
      if (currentPage !== 1) setCurrentPage(1);
    } else if (currentPage > newTotalPages) {
      setCurrentPage(newTotalPages);
    }
  }, [allFilteredOrders.length, currentPage]);

  useEffect(() => {
    if (highlightedOrderId) {
      const timer = setTimeout(() => {
        setHighlightedOrderId(null);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [highlightedOrderId]);

  const handlePageChange = (pageNumber: number) => {
    if (pageNumber < 1 || pageNumber > totalPages) return;
    setCurrentPage(pageNumber);
  };

  const handleCreateOrder = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!newOrder.customerName || !newOrder.productName || !newOrder.price || !newOrder.quantity) {
      toast.error('Please fill in all required fields');
      return;
    }
    try {
      const response = await fetch(`${API}/api/orders/add`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newOrder),
      });
      if (!response.ok) throw new Error('Order creation failed');
      toast.success('Order created successfully!');
      const res = await fetch(`${API}/api/orders`);
      const data = await res.json();
      setOrders(Array.isArray(data) ? data : []);
      setIsModalOpen(false);
      setNewOrder(getEmptyOrder());
    } catch {
      toast.error('Order creation failed!');
    }
  };

  const handleDeleteOrder = (orderId: string) => {
    setOrderToDeleteId(orderId);
    setIsDeleteModalOpen(true);
  };

  // Order silme işlemini API ile yap:
  const confirmActualDelete = async () => {
    if (orderToDeleteId) {
      try {
        await fetch(`${API}/api/orders/softdelete${orderToDeleteId}`, { method: 'DELETE' });
        toast.error(`Order ${orderToDeleteId} deleted successfully!`);
        const res = await fetch(`${API}/api/order`);
        const data = await res.json();
        setOrders(Array.isArray(data) ? data : []);
      } catch {
        toast.error('Order deletion failed!');
      }
      setOrderToDeleteId(null);
      setIsDeleteModalOpen(false);
    }
  };
  const closeDeleteModal = () => {
    setOrderToDeleteId(null);
    setIsDeleteModalOpen(false);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setNewOrder(getEmptyOrder());
  };
  const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
  const endIndex = startIndex + ITEMS_PER_PAGE;

  const handleEditOrder = (order: Order) => {
    setOrderToEdit(order);
    setIsEditModalOpen(true);
  };

  // const handleSaveOrder = async (updatedOrder: Order) => {
  //   try {
  //     await fetch(`http://localhost:5000/api/orders/${updatedOrder.id}`, {
  //       method: 'PUT',
  //       headers: { 'Content-Type': 'application/json' },
  //       body: JSON.stringify(updatedOrder),
  //     });
  //     setOrders(prev => prev.map(o => o.id === updatedOrder.id ? updatedOrder : o));
  //     setIsEditModalOpen(false);
  //     setOrderToEdit(null);
  //     toast.success('Order updated!');
  //   } catch {
  //     toast.error('Order update failed!');
  //   }
  // };

  const handleProductInputChange = handleInputChangeGeneric(setNewProduct);

  const handleInputChange = handleInputChangeGeneric(setNewOrder);

  const handleCreateProduct = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!newProduct.productName || !newProduct.price || !newProduct.size || newProduct.size.trim() === '') {
      toast.error('Please fill in all required fields (Product Name, Price, and Size)');
      return;
    }
    try {
      const formData = new FormData();
      Object.entries(newProduct).forEach(([key, value]) => {
        if (typeof value === 'function') return;
        formData.append(key, value != null ? String(value) : '');
      });

      const result = await createProduct(formData);
      if (result.success) {
        toast.success('Product created successfully!');
        const data = await getProducts();
        const mapped = Array.isArray(data)
          ? data.map((item: any) => ({
              productId: item.id?.toString() ?? '',
              productName: item.productName || '',
              price: item.price || '',
              size: item.size || '',
              thumbnail: item.thumbnail || '',
              stock: item.stock || '',
              blockNumber: item.blockNumber || '',
              pieceNumber: item.pieceNumber || '',
              color: item.color || '',
              productModelId: item.productModelId || '',
            }))
          : [];
        setProducts(mapped);
        setIsProductModalOpen(false);
        setNewProduct(getEmptyProduct());
      } else {
        toast.error(result.message || 'Product creation failed!');
      }
    } catch (err) {
      console.error('Product creation error:', err);
      toast.error('Product creation failed!');
    }
  };

  const handleDeleteProduct = (productId: string) => {
    setProductToDeleteId(productId);
    setIsProductDeleteModalOpen(true);
  };

  const confirmProductDelete = async (productId: string) => {
    try {
      await deleteProduct(productId);
      
      const data = await getProducts();
      const mapped = Array.isArray(data)
        ? data.map((item: any) => ({
            productId: item.id?.toString() ?? '',
            productName: item.productName || '',
            price: item.price || '',
            size: item.size || '',
            thumbnail: item.thumbnail || '',
            stock: item.stock || '',
            blockNumber: item.blockNumber || '',
            pieceNumber: item.pieceNumber || '',
            color: item.color || '',
            productModelId: item.productModelId || '',
          }))
        : [];
      setProducts(mapped);
      toast.success('Product deleted successfully!');
    } catch {
      toast.error('Product deletion failed!');
    }
  };

  const closeProductDeleteModal = () => {
    setProductToDeleteId(null);
    setIsProductDeleteModalOpen(false);
  };

  return (
    <div className="flex h-screen bg-gray-50 overflow-hidden">
      <Toaster position="top-center" />
      {/* Sidebar */}

      <Sidebar 
        isOpen={isSidebarOpen}
        setIsOpen={setIsSidebarOpen}
        activeContent={activeContent}
        onItemClick={(key) => {
          setActiveContent(key);
          setIsSidebarOpen(false);
          setCurrentPage(1);
          setSearchTerm('');
          setActiveTab(TAB_NAMES.TOTAL);
          
        }}
        items={sidebarItemsDefinition}
      />
      {/* Main Content */}
      <div className="flex-1 flex flex-col min-w-0">
        {/* Mobile Header */}
        <MobileHeader 
          onOpenSidebar={() => setIsSidebarOpen(true)}
          activeContent={activeContent}
          showCreateOrderButton={activeContent === 'Orders'}
          onCreateOrderClick={() => setIsModalOpen(true)}
        />
        <div className="flex-1 overflow-y-auto">
          <div className="p-4 lg:p-8">
            {/* Desktop Header */}
            <DesktopHeader 
              activeContent={activeContent}
              showCreateOrderButton={activeContent === 'Orders'}
              onCreateOrderClick={() => setIsModalOpen(true)}
            />
            {/* Dashboard Content */}
            {/* {activeContent === 'Dashboard' && (
              <ContentPlaceholder title="Welcome to the Dashboard!" type="dashboard" />
            )} */}
            {/* Orders Content */}
            {activeContent === 'Orders' && (
              <>
                {/* <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 lg:gap-6 mb-6 lg:mb-8">
                  <StatCard 
                    title="Total Orders" 
                    value={allFilteredOrders.length}
                    change="29.7%" 
                    isPositive={true}
                    color="bg-blue-100"
                  /> 
                  <StatCard 
                    title="Completed" 
                    value={orders.filter(o => o.status === ORDER_STATUS.COMPLETED).length}
                    change="30.2%" 
                    isPositive={true}
                    color="bg-green-100"
                  /> 
                  <StatCard 
                    title="Pending" 
                    value={orders.filter(o => o.status === ORDER_STATUS.PENDING).length}
                    change="15.2%" 
                    isPositive={true}
                    color="bg-yellow-100"
                  /> 
                  <StatCard 
                    title="Cancelled" 
                    value={orders.filter(o => o.status === ORDER_STATUS.CANCELLED).length}
                    change="10.2%" 
                    isPositive={false}
                    color="bg-red-100"
                  />
                </div> */}
                <div className="bg-white rounded-lg shadow-sm border border-gray-200">
                  <OrderListControls 
                    tabs={[
                      { label: TAB_NAMES.TOTAL, count: orders.length },
                      { label: TAB_NAMES.COMPLETED, count: orders.filter(o => o.status === ORDER_STATUS.COMPLETED).length },
                      { label: TAB_NAMES.PENDING, count: orders.filter(o => o.status === ORDER_STATUS.PENDING).length },
                    ]}
                    activeTab={activeTab}
                    onTabClick={(tab) => { setActiveTab(tab); setCurrentPage(1); }}
                    searchTerm={searchTerm}
                    onSearchChange={(term) => { setSearchTerm(term); setCurrentPage(1); }}
                    orders={orders} 
                  />
                  {/* Table (Desktop) */}
                  <div className="hidden lg:block">
                    <OrderTable 
                      orders={paginatedOrders}
                      highlightedOrderId={highlightedOrderId}
                      getStatusColorClass={getStatusColorClass}
                      onDeleteOrder={handleDeleteOrder}
                      onEditOrder={handleEditOrder}
                    />
                  </div>
                  {/* Card List (Mobile) */}
                  <div className="lg:hidden">
                    <OrderCardList 
                      orders={paginatedOrders}
                      highlightedOrderId={highlightedOrderId}
                      getStatusColorClass={getStatusColorClass}
                      onDeleteOrder={handleDeleteOrder}
                      onEditOrder={handleEditOrder}
                    />
                  </div>
                  <PaginationControls 
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                    totalResults={allFilteredOrders.length}
                    itemsPerPage={ITEMS_PER_PAGE}
                  />
                </div>
              </>
            )}
            {/* Products Content */}
            {activeContent === 'Products' && (
              <>
                <div className="flex justify-end mb-4 gap-2 items-center">
                  <div className="relative flex items-center gap-2">
                    <button
                      onClick={() => setShowProductSearch((v) => !v)}
                      className="bg-gray-100 text-gray-700 px-4 py-2 rounded hover:bg-gray-200 transition font-semibold border border-gray-300 flex items-center gap-2"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-4-4m0 0A7 7 0 104 4a7 7 0 0013 13z" /></svg>
                      Search
                    </button>
                    {showProductSearch && (
                      <div className="absolute left-full ml-2 w-64 z-10">
                        <ProductListControls
                          searchTerm={productSearchTerm}
                          onSearchChange={setProductSearchTerm}
                        />
                      </div>
                    )}
                  </div>
                  <button
                    onClick={() => setIsProductModalOpen(true)}
                    className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition font-semibold"
                  >
                    Create Product
                  </button>
                </div>
                {(() => {
                  const filteredProducts = !productSearchTerm.trim()
                    ? products
                    : products.filter(p =>
                        p.productName.toLowerCase().includes(productSearchTerm.toLowerCase()) ||
                        (p.productModelId && p.productModelId).toString().includes(productSearchTerm.toLowerCase())
                      );
                  return filteredProducts.length === 0 ? (
                    "No products found."
                  ) : (
                    <ProductTable products={products} onDeleteProduct={confirmProductDelete} />
                  );
                })()}
                <DeleteConfirmationModal 
                  isOpen={isProductDeleteModalOpen}
                  orderIdToDelete={productToDeleteId}
                  onClose={closeProductDeleteModal}
                  onConfirm={() => {
                    if (productToDeleteId) {
                      confirmProductDelete(productToDeleteId);
                    }
                  }}
                />

              </>
            )}
            {/* { Settings Content
            {activeContent === 'Settings' && (
              <ContentPlaceholder title="Settings" type="settings" />
            )} } */}
            {/* Other Content */}
            {/* {activeContent !== 'Dashboard' && activeContent !== 'Orders' && activeContent !== 'Settings' && (
              <ContentPlaceholder title={`Welcome to ${activeContent}!`} type="generic" />
            )} */}
          </div>
        </div>
      </div>
      {/* Create Order Modal */}
      <CreateOrderModal 
        isOpen={isModalOpen}
        onClose={closeModal}
        onSubmit={handleCreateOrder}
        formData={newOrder}
        onInputChange={handleInputChange}
      />
      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal 
        isOpen={isDeleteModalOpen}
        orderIdToDelete={orderToDeleteId}
        onClose={closeDeleteModal}
        onConfirm={confirmActualDelete}
      />
      {/* <EditOrderModal
        isOpen={isEditModalOpen}
        order={orderToEdit}
        onClose={() => { setIsEditModalOpen(false); setOrderToEdit(null); }}
        onSave={handleSaveOrder}
      /> */}
      <CreateProductModal
        isOpen={isProductModalOpen}
        onClose={() => setIsProductModalOpen(false)}
        onSubmit={handleCreateProduct}
        formData={newProduct}
        onInputChange={handleProductInputChange}
      />
    </div>
  );
};

export default FlowStockDashboard;
