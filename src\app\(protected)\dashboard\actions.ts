"use server"

import { cookies } from "next/headers";

export async function createProduct(formData: FormData) {
  try {
    const token = (await cookies()).get("token")?.value;

    if (!token) {
      return { success: false, message: "Authentication required" };
    }

    const sizeValue = formData.get("size")?.toString() || "";
    const thumbnailFile = formData.get("thumbnail") as File | null;

    console.log(thumbnailFile)

    const productData = {
      productName: formData.get("productName")?.toString() || "",
      price: Number(formData.get("price")) || 0,
      Size: sizeValue.trim() || "",
      thumbnail: thumbnailFile,
      stock: Number(formData.get("stock")) || 0,
      blockNumber: Number(formData.get("blockNumber")) || 0,
      pieceNumber: Number(formData.get("pieceNumber")) || 0,
      color: formData.get("color")?.toString() || "",
      productModelId: Number(formData.get("productModelId")) || 0,
    };


    if (!productData.productName || !productData.price) {
      return { success: false, message: "Product name and price are required" };
    }

    if ((!productData.Size || productData.Size.trim() === "") && (!productData.Size || productData.Size.trim() === "")) {
      return { success: false, message: "Size field is required" };
    }

  

    try {
      const formDataToSend = new FormData();
      formDataToSend.append("ProductName", productData.productName);
      formDataToSend.append("Price", productData.price.toString());
      formDataToSend.append("Size", productData.Size);

      if (thumbnailFile && thumbnailFile.size > 0) {
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
        if (!allowedTypes.includes(thumbnailFile.type)) {
          return { success: false, message: "Only .jpg and .png files are allowed for thumbnail" };
        }
        formDataToSend.append("Thumbnail", thumbnailFile);
      } else {
        
        const fakeFile = new File([], "placeholder.jpg", { type: 'image/jpeg' });
        formDataToSend.append("Thumbnail", fakeFile);
      } 

      formDataToSend.append("Stock", productData.stock.toString());
      formDataToSend.append("BlockNumber", productData.blockNumber.toString());
      formDataToSend.append("PieceNumber", productData.pieceNumber.toString());

      const colorId = productData.color && productData.color !== "0" ? productData.color : "1";
      formDataToSend.append("ColorId", colorId);
      formDataToSend.append("ProductModelId", productData.productModelId.toString());


      // for (let [key, value] of formDataToSend.entries()) {
      //   console.log(`${key}:`, value instanceof File ? `File(${value.name}, ${value.size} bytes)` : value);
      // }

      const response = await fetch(`http://localhost:5000/api/product/add`, {
        method: "POST",
        headers: {
          Authorization: "Bearer " + token,
        },
        body: formDataToSend,
      });

      let data = null;
      const responseText = await response.text();
     
      try {
        if (responseText.trim()) {
          data = JSON.parse(responseText);
        } else {
          data = { message: "Product created successfully" };
        }
      } catch (e) {
        return {
          success: false,
          message: `Invalid response from server. Status: ${response.status}, Response: ${responseText.substring(0, 500)}`
        };
      }

      if (!response.ok) {
        const errorMessage = data?.errors
          ? Object.values(data.errors).flat().join(", ")
          : data?.message || "Product creation failed";
        return { success: false, message: errorMessage };
      }

      return { success: true, data: data };
    } catch (error) {
      return {
        success: false,
        data: {
          id: Date.now(),
          ...productData,
          message: "Product created successfully (mock)"
        }
      };
    }
  } catch (error) {
    return { success: false, message: "Product creation failed" };
  }
}
