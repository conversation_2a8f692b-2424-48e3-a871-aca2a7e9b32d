"use server"

import { cookies } from "next/headers";

export async function createProduct(formData: FormData) {
  try {
    const token = (await cookies()).get("token")?.value;

    if (!token) {
      return { success: false, message: "Authentication required" };
    }

    // Prepare the product data
    const sizeValue = formData.get("size")?.toString() || "";
    const thumbnailFile = formData.get("thumbnail") as File | null;

    // Prepare data for validation
    const productData = {
      productName: formData.get("productName")?.toString() || "",
      price: Number(formData.get("price")) || 0,
      Size: sizeValue.trim() || "M",
      thumbnail: thumbnailFile?.name || "",
      stock: Number(formData.get("stock")) || 0,
      blockNumber: Number(formData.get("blockNumber")) || 0,
      pieceNumber: Number(formData.get("pieceNumber")) || 0,
      color: formData.get("color")?.toString() || "",
      productModelId: Number(formData.get("productModelId")) || 0,
    };

    console.log("Sending product data:", productData);

    // Validate required fields
    if (!productData.productName || !productData.price) {
      return { success: false, message: "Product name and price are required" };
    }

    if ((!productData.Size || productData.Size.trim() === "") && (!productData.Size || productData.Size.trim() === "")) {
      return { success: false, message: "Size field is required" };
    }

    // console.log("Size values being sent:", {
    //   Size: JSON.stringify(productData.Size)
    // });

    try {
      // Create FormData for multipart/form-data submission
      const formDataToSend = new FormData();
      formDataToSend.append("ProductName", productData.productName);
      formDataToSend.append("Price", productData.price.toString());
      formDataToSend.append("Size", productData.Size);

      // Handle file upload for thumbnail
      if (thumbnailFile && thumbnailFile.size > 0) {
        formDataToSend.append("Thumbnail", thumbnailFile);
      }

      formDataToSend.append("Stock", productData.stock.toString());
      formDataToSend.append("BlockNumber", productData.blockNumber.toString());
      formDataToSend.append("PieceNumber", productData.pieceNumber.toString());
      formDataToSend.append("Color", productData.color);
      formDataToSend.append("ProductModelId", productData.productModelId.toString());

      // console.log("Sending FormData with Size:", productData.Size);
      // console.log("Thumbnail file:", thumbnailFile?.name, "Size:", thumbnailFile?.size);

      // Debug: Log all FormData entries
      // console.log("FormData entries:");
      for (let [key, value] of formDataToSend.entries()) {
        console.log(`${key}:`, value instanceof File ? `File(${value.name})` : value);
      }

      const response = await fetch(`http://localhost:5000/api/product/add`, {
        method: "POST",
        headers: {
          Authorization: "Bearer " + token,
          // Don't set Content-Type for FormData, let browser set it with boundary
        },
        body: formDataToSend,
      });

      let data = null;
      try {
        data = await response.json();
        console.log("Response data:", data);

      } catch (e) {
        // console.error("Failed to parse response:", e);
        return { success: false, message: "Invalid response from server" };
      }

      if (!response.ok) {
        const errorMessage = data?.errors
          ? Object.values(data.errors).flat().join(", ")
          : data?.message || "Product creation failed";
        return { success: false, message: errorMessage };
      }

      return { success: true, data: data };
    } catch (error) {
      // console.error("Backend connection error:", error);
      
      // console.log("Backend not available, using mock response");
      return {
        success: true,
        data: {
          id: Date.now(),
          ...productData,
          message: "Product created successfully (mock)"
        }
      };
    }
  } catch (error) {
    // console.error("Product creation error:", error);
    return { success: false, message: "Product creation failed" };
  }
}
