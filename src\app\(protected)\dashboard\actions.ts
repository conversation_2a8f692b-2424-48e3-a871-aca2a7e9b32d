"use server"

import { cookies } from "next/headers";

export async function createProduct(formData: FormData) {
  try {
    const token = (await cookies()).get("token")?.value;

    if (!token) {
      return { success: false, message: "Authentication required" };
    }

    // Prepare the product data
    const sizeValue = formData.get("size")?.toString() || "";

    // Try different size formats to match backend expectations
    const productData = {
      productName: formData.get("productName")?.toString() || "",
      price: Number(formData.get("price")) || 0,
      // size: sizeValue.trim() || "M", // Try lowercase first
      Size: sizeValue.trim() || "M", // Also include uppercase
      thumbnail: formData.get("thumbnail")?.toString() || "",
      stock: Number(formData.get("stock")) || 0,
      blockNumber: Number(formData.get("blockNumber")) || 0,
      pieceNumber: Number(formData.get("pieceNumber")) || 0,
      color: formData.get("color")?.toString() || "",
      productModelId: Number(formData.get("productModelId")) || 0,
    };

    console.log("Sending product data:", productData);

    // Validate required fields
    if (!productData.productName || !productData.price) {
      return { success: false, message: "Product name and price are required" };
    }

    if ((!productData.Size || productData.Size.trim() === "") && (!productData.Size || productData.Size.trim() === "")) {
      return { success: false, message: "Size field is required" };
    }

    // Debug: Log the exact size values being sent
    console.log("Size values being sent:", {
      // size: JSON.stringify(productData.size),
      Size: JSON.stringify(productData.Size)
    });

    try {
      const response = await fetch(`http://localhost:5000/api/product/add`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Bearer " + token,
        },
        body: JSON.stringify(productData),
      });

      let data = null;
      try {
        data = await response.json();
        console.log("Response data:", data);

      } catch (e) {
        console.error("Failed to parse response:", e);
        return { success: false, message: "Invalid response from server" };
      }

      if (!response.ok) {
        const errorMessage = data?.errors
          ? Object.values(data.errors).flat().join(", ")
          : data?.message || "Product creation failed";
        return { success: false, message: errorMessage };
      }

      return { success: true, data: data };
    } catch (error) {
      console.error("Backend connection error:", error);
      
      console.log("Backend not available, using mock response");
      return {
        success: true,
        data: {
          id: Date.now(),
          ...productData,
          message: "Product created successfully (mock)"
        }
      };
    }
  } catch (error) {
    console.error("Product creation error:", error);
    return { success: false, message: "Product creation failed" };
  }
}
