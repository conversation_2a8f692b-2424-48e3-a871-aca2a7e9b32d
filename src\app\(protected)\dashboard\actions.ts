"use server"
import { FormEvent } from "react";
import httpClient from "../../../../axios";

export function createProduct(formEvent: FormEvent<HTMLFormElement>) {
  formEvent.preventDefault();
  const formData = new FormData(formEvent.currentTarget);
  httpClient.postForm(`http://localhost:5000/api/product/add`, {
    productName: formData.get("productName"),
    price: formData.get("price"),
    size: formData.get("size"),
    thumbnail: formData.get("thumbnail"),
    stock: formData.get("stock"),
    blockNumber: formData.get("blockNumber"),
    pieceNumber: formData.get("pieceNumber"),
    color: formData.get("color"),
    productModelId: formData.get("productModelId"),
});
  // // const token = (await cookies()).get("token")?.value;

  // console.log(product);
  
  // if (token) {
  //   const response = await fetch(`${myAPI}/api/product/add`, {
  //     method: "POST",
  //     headers: {
  //       "Content-Type": "application/json",
  //       Authorization: "Bearer " + token,
  //     },
  //     body: JSON.stringify(product),
  //   });
    
  //   // console.log(response);

  //   let data = null;
  //   try {
  //     data = await response.json();
  //     console.log(data);
  //   } catch (e) {
  //     return "Product creation failed";
  //   }
  
  //   if (!response.ok) {
  //     return (data && data.message) ? data.message : "Product creation failed";
  //   }
  //   return data;
  // }
  return 'ok';
}
