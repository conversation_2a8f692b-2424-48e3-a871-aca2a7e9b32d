"use server"

import { cookies } from "next/headers";

export async function createProduct(formData: FormData) {
  try {
    const token = (await cookies()).get("token")?.value;
    console.log("Token found:", token ? "Yes" : "No");

    if (!token) {
      return { success: false, message: "Authentication required" };
    }

    // Prepare the product data
    const sizeValue = formData.get("size")?.toString() || "";
    const thumbnailFile = formData.get("thumbnail") as File | null;

    const productData = {
      productName: formData.get("productName")?.toString() || "",
      price: Number(formData.get("price")) || 0,
      Size: sizeValue.trim() || "M",
      thumbnail: thumbnailFile?.name || "",
      stock: Number(formData.get("stock")) || 0,
      blockNumber: Number(formData.get("blockNumber")) || 0,
      pieceNumber: Number(formData.get("pieceNumber")) || 0,
      color: formData.get("color")?.toString() || "",
      productModelId: Number(formData.get("productModelId")) || 0,
    };

    console.log(productData);

    if (!productData.productName || !productData.price) {
      return { success: false, message: "Product name and price are required" };
    }

    if ((!productData.Size || productData.Size.trim() === "") && (!productData.Size || productData.Size.trim() === "")) {
      return { success: false, message: "Size field is required" };
    }

  

    try {
      const formDataToSend = new FormData();
      formDataToSend.append("ProductName", productData.productName);
      formDataToSend.append("Price", productData.price.toString());
      formDataToSend.append("Size", productData.Size);

      if (thumbnailFile && thumbnailFile.size > 0) {

        const allowedTypes = ['image/jpeg',  'image/jpg', 'image/png'];
        if (!allowedTypes.includes(thumbnailFile.type)) {
          return { success: false, message: "Only .jpg and .png files are allowed for thumbnail" };
        }
        formDataToSend.append("Thumbnail", thumbnailFile);
      } else {
        console.log("No thumbnail file provided, continuing without it");
      }

      formDataToSend.append("Stock", productData.stock.toString());
      formDataToSend.append("BlockNumber", productData.blockNumber.toString());
      formDataToSend.append("PieceNumber", productData.pieceNumber.toString());

      const colorId = productData.color && productData.color !== "0" ? productData.color : "1";
      formDataToSend.append("ColorId", colorId);
      formDataToSend.append("ProductModelId", productData.productModelId.toString());

      formDataToSend.append("Size", productData.Size);
      formDataToSend.append("Thumbnail", productData.thumbnail);

      // for (let [key, value] of formDataToSend.entries()) {
      //   console.log(`${key}:`, value instanceof File ? `File(${value.name})` : value);
      // }

      const response = await fetch(`http://localhost:5000/api/product/add`, {
        method: "POST",
        headers: {
          Authorization: "Bearer " + token,
        },
        body: formDataToSend,
      });

      let data = null;
      try {
        data = await response.json();
        
        console.log("Response data:", data);

      } catch (e) {
        return { success: false, message: "Invalid response from server" };
      }

      if (!response.ok) {
        const errorMessage = data?.errors
          ? Object.values(data.errors).flat().join(", ")
          : data?.message || "Product creation failed";
        return { success: false, message: errorMessage };
      }

      return { success: true, data: data };
    } catch (error) {
      
      return {
        success: true,
        data: {
          id: Date.now(),
          ...productData,
          message: "Product created successfully (mock)"
        }
      };
    }
  } catch (error) {
    // console.error("Product creation error:", error);
    return { success: false, message: "Product creation failed" };
  }
}
