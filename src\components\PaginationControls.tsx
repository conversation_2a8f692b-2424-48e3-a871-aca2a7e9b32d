import React from 'react';

type PaginationControlsProps = {
  currentPage: number;
  totalPages: number;
  onPageChange: (pageNumber: number) => void;
  totalResults: number;
  itemsPerPage: number;
};

export const PaginationControls: React.FC<PaginationControlsProps> = ({ currentPage, totalPages, onPageChange, totalResults, itemsPerPage }) => {
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, totalResults);

  if (totalPages <= 0 && totalResults === 0) {
     return (
        <div className="px-4 lg:px-6 py-4 border-t border-gray-200 flex flex-col sm:flex-row items-center justify-between gap-3">
            <div className="text-sm text-gray-700">Showing 0 to 0 of 0 results</div>
        </div>
     );
  }

  return (
    <div className="px-4 lg:px-6 py-4 border-t border-gray-200 flex flex-col sm:flex-row items-center justify-between gap-3">
      <div className="text-sm text-gray-700">
        Showing {totalResults > 0 ? startIndex + 1 : 0} to {endIndex} of {totalResults} results
      </div>
      <div className="flex items-center space-x-1 sm:space-x-2">
        <button onClick={() => onPageChange(currentPage - 1)} disabled={currentPage === 1} className="px-2 sm:px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">Prev</button>
        {totalPages > 0 && [...Array(totalPages)].map((_, i) => (
          <button key={i + 1} onClick={() => onPageChange(i + 1)} className={`px-2 sm:px-3 py-1 text-sm rounded ${currentPage === i + 1 ? 'bg-blue-600 text-white' : 'border border-gray-300 hover:bg-gray-50'}`}>{i + 1}</button>
        ))}
        <button onClick={() => onPageChange(currentPage + 1)} disabled={currentPage === totalPages || totalPages === 0} className="px-2 sm:px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed">Next</button>
      </div>
    </div>
  );
};